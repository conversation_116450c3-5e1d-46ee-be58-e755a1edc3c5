import { expect } from '@playwright/test';
import { q, test } from 'agentq_web_automation_test';
import axios from 'axios';

// Load test data from environment
const testDataEnv = process.env.TEST_DATA;
let stepsData: any = null;

if (testDataEnv) {
  try {
    stepsData = JSON.parse(testDataEnv);
    console.log(`Loaded test data for: ${stepsData.testCase.title}`);
  } catch (error) {
    console.error('Failed to parse test data:', error);
  }
}

const BACKEND_URL = process.env.BACKEND_URL || 'http://localhost:3010';

test(stepsData?.testCase?.title || 'Dynamic Test Case', async ({ page }) => {
  console.log('🚀 Starting native test execution');

  // Set page context for AgentQ library
  (global as any).currentPage = page;

  // Verify JWT token if available
  const jwtToken = process.env.AGENTQ_JWT_TOKEN || process.env.AUTH_TOKEN;
  if (jwtToken) {
    console.log('🔍 JWT Token available: true');
    console.log('🔍 JWT Token value:', jwtToken.substring(0, 20) + '...');

    try {
      console.log('Verifying JWT token with:', `${BACKEND_URL}/auth/verify`);
      await axios.post(`${BACKEND_URL}/auth/verify`, {}, {
        headers: {
          'Authorization': `Bearer ${jwtToken}`
        }
      });
      console.log('✅ JWT token verification successful');
    } catch (verifyError) {
      console.warn('⚠️ JWT token verification failed:', verifyError);
    }
  }

  if (!stepsData) {
    console.log('⚠️ No test data provided, running simple demo test');
    // Run a simple demo test
    await page.goto('https://example.com');
    await expect(page).toHaveTitle(/Example/);
    console.log('✅ Demo test completed successfully');
    return;
  }

  // Debug: Log the full test data structure
  console.log('🔍 Debug: Full test data structure:', JSON.stringify(stepsData, null, 2));

  if (!stepsData.steps || !Array.isArray(stepsData.steps)) {
    console.log('⚠️ No valid steps array found in test data');
    console.log('🔍 Debug: stepsData.steps type:', typeof stepsData.steps);
    console.log('🔍 Debug: stepsData.steps value:', stepsData.steps);

    // Try to find steps in different locations
    const possibleSteps = stepsData.testSteps || stepsData.actions || stepsData.commands;
    if (possibleSteps && Array.isArray(possibleSteps)) {
      console.log('✅ Found steps in alternative location, using those');
      stepsData.steps = possibleSteps;
    } else {
      console.log('⚠️ No valid steps found, running simple demo test');
      await page.goto('https://example.com');
      await expect(page).toHaveTitle(/Example/);
      console.log('✅ Demo test completed successfully');
      return;
    }
  }

  try {
    console.log(`📋 Executing ${stepsData.steps.length} test steps`);

    // Debug: Log the structure of the first few steps
    console.log('🔍 Debug: First few steps structure:', JSON.stringify(stepsData.steps.slice(0, 3), null, 2));

    // Execute each step
    for (let i = 0; i < stepsData.steps.length; i++) {
      const step = stepsData.steps[i];

      // Skip if step is not an object (e.g., if it's a string character)
      if (typeof step !== 'object' || step === null) {
        console.log(`⚠️ Step ${i + 1} is not a valid object, skipping:`, step);
        continue;
      }

      // Debug: Log each step structure
      console.log(`🔍 Debug: Processing step ${i + 1}:`, JSON.stringify(step, null, 2));

      // Handle different property names for action
      const action = step.action || step.stepName || step.type;
      const target = step.target || step.selector || step.element;
      const value = step.value || step.text || step.input;
      const prompt = step.prompt || (action === 'prompt' ? value : null);

      console.log(`🔍 Debug: Step ${i + 1} extracted values - action:`, action, 'target:', target, 'value:', value, 'prompt:', prompt);

      if (action === 'prompt' && (prompt || value)) {
        const promptText = prompt || value;
        console.log(`🌐 Step: prompt ${promptText} ✓`);
        await q(promptText);
      } else if ((action === 'Go to Page' || action === 'goto' || action === 'navigate' || action === 'visit') && target) {
        console.log(`Step: goto ${target} ✓`);
        await page.goto(target, { timeout: 30000 });
      } else if ((action === 'Fill' || action === 'write' || action === 'type' || action === 'input') && target && value) {
        console.log(`Step: fill ${target} ${value} ✓`);
        await page.fill(target, value);
      } else if ((action === 'Click' || action === 'click' || action === 'tap') && target) {
        console.log(`Step: click ${target} ✓`);
        await page.click(target);
      } else if ((action === 'assertText' || action === 'assert' || action === 'verify' || action === 'expect') && target && value) {
        console.log(`Step: assertText ${target} ${value} ✓`);
        await expect(page.locator(target)).toHaveText(value);
      } else {
        console.log(`Step ${i + 1}: ${action || 'undefined'} - Skipped (unsupported action)`);
        console.log(`🔍 Debug: Full step ${i + 1} object:`, step);
      }
    }

    console.log('✅ All test steps completed successfully');

  } catch (error) {
    console.error('❌ Test failed:', error);
    throw error;
  }
});
